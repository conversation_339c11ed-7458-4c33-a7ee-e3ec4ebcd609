package services

import (
	"gitlab.finema.co/finema/csp/csp-api/models"
	"gitlab.finema.co/finema/csp/csp-api/repo"
	core "gitlab.finema.co/finema/idin-core"
	"gitlab.finema.co/finema/idin-core/repository"
)

type ICycleService interface {
	Find(id string) (*models.Cycle, core.IError)
	Pagination(pageOptions *core.PageOptions) (*repository.Pagination[models.Cycle], core.IError)
	FindCurrent() (*models.Cycle, core.IError)
	FindByDateRange(startDate, endDate string, pageOptions *core.PageOptions) (*repository.Pagination[models.Cycle], core.IError)
}

type cycleService struct {
	ctx core.IContext
}

func (s cycleService) Find(id string) (*models.Cycle, core.IError) {
	return repo.Cycle(s.ctx, repo.CycleWithProjectUsages()).FindOne("id = ?", id)
}

func (s cycleService) Pagination(pageOptions *core.PageOptions) (*repository.Pagination[models.Cycle], core.IError) {
	return repo.Cycle(
		s.ctx,
		repo.CycleOrderBy(pageOptions),
		repo.CycleBySearch(pageOptions.Q),
		repo.CycleWithLatestUsage()).
		Pagination(pageOptions)
}

func (s cycleService) FindCurrent() (*models.Cycle, core.IError) {
	return repo.Cycle(s.ctx, repo.CycleCurrent(), repo.CycleWithProjectUsages()).FindOne()
}

func (s cycleService) FindByDateRange(startDate, endDate string, pageOptions *core.PageOptions) (*repository.Pagination[models.Cycle], core.IError) {
	return repo.Cycle(
		s.ctx,
		repo.CycleOrderBy(pageOptions),
		repo.CycleByDateRange(startDate, endDate),
		repo.CycleBySearch(pageOptions.Q)).
		Pagination(pageOptions)
}

func NewCycleService(ctx core.IContext) ICycleService {
	return &cycleService{ctx: ctx}
}
